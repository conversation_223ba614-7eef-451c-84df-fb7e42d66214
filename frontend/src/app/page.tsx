'use client'

import { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { Music, Sparkles, Heart, Zap, Download, Play } from 'lucide-react'
import LyricsCreator from '@/components/LyricsCreator'

export default function Home() {
  const [showCreator, setShowCreator] = useState(false)
  const [windowSize, setWindowSize] = useState({ width: 1200, height: 800 })
  const [isClient, setIsClient] = useState(false)

  useEffect(() => {
    // クライアントサイドでのみwindowオブジェクトにアクセス
    setIsClient(true)
    setWindowSize({
      width: window.innerWidth,
      height: window.innerHeight
    })

    const handleResize = () => {
      setWindowSize({
        width: window.innerWidth,
        height: window.innerHeight
      })
    }

    window.addEventListener('resize', handleResize)
    return () => window.removeEventListener('resize', handleResize)
  }, [])

  if (showCreator) {
    return <LyricsCreator onBack={() => setShowCreator(false)} />
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 relative overflow-hidden">
      {/* 背景のアニメーション要素 */}
      <div className="absolute inset-0">
        <div className="absolute top-20 left-20 w-72 h-72 bg-pink-500/20 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute bottom-20 right-20 w-96 h-96 bg-cyan-500/20 rounded-full blur-3xl animate-pulse delay-1000"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-80 h-80 bg-yellow-500/10 rounded-full blur-3xl animate-pulse delay-2000"></div>
      </div>

      {/* メインコンテンツ */}
      <div className="relative z-20 flex flex-col items-center justify-center min-h-screen px-4">
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 1 }}
          className="text-center mb-12"
        >
          {/* ロゴ・タイトル */}
          <motion.div
            initial={{ scale: 0.8 }}
            animate={{ scale: 1 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="flex items-center justify-center mb-6"
          >
            <div className="relative">
              <Music className="w-16 h-16 text-pink-400 mr-4" />
              <Sparkles className="w-8 h-8 text-yellow-400 absolute -top-2 -right-2 animate-pulse" />
            </div>
            <h1 className="text-6xl font-bold bg-gradient-to-r from-pink-400 via-purple-400 to-cyan-400 bg-clip-text text-transparent">
              AI歌詞クリエーター
            </h1>
          </motion.div>

          <motion.p
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.8, delay: 0.5 }}
            className="text-xl text-gray-300 mb-8 max-w-2xl mx-auto leading-relaxed"
          >
            あなたの想いを音楽に。AIが紡ぐ、心に響く歌詞の世界へようこそ。
            <br />
            J-POPの深層構造を理解したAIが、あなただけの物語を歌詞に変えます。
          </motion.p>

          {/* 特徴アイコン */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.8 }}
            className="flex justify-center space-x-8 mb-12"
          >
            <div className="flex flex-col items-center">
              <div className="w-16 h-16 bg-gradient-to-br from-pink-500 to-purple-600 rounded-full flex items-center justify-center mb-2">
                <Heart className="w-8 h-8 text-white" />
              </div>
              <span className="text-gray-300 text-sm">感情豊か</span>
            </div>
            <div className="flex flex-col items-center">
              <div className="w-16 h-16 bg-gradient-to-br from-cyan-500 to-blue-600 rounded-full flex items-center justify-center mb-2">
                <Zap className="w-8 h-8 text-white" />
              </div>
              <span className="text-gray-300 text-sm">瞬時生成</span>
            </div>
            <div className="flex flex-col items-center">
              <div className="w-16 h-16 bg-gradient-to-br from-yellow-500 to-orange-600 rounded-full flex items-center justify-center mb-2">
                <Download className="w-8 h-8 text-white" />
              </div>
              <span className="text-gray-300 text-sm">簡単保存</span>
            </div>
          </motion.div>

          {/* 開始ボタン */}
          <motion.button
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.8, delay: 1.1 }}
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            onClick={() => setShowCreator(true)}
            className="group relative px-12 py-4 bg-gradient-to-r from-pink-500 via-purple-500 to-cyan-500 rounded-full text-white font-bold text-lg shadow-2xl hover:shadow-pink-500/25 transition-all duration-300"
          >
            <span className="relative z-10 flex items-center">
              <Play className="w-6 h-6 mr-2 group-hover:animate-pulse" />
              歌詞を作成する
            </span>
            <div className="absolute inset-0 bg-gradient-to-r from-pink-600 via-purple-600 to-cyan-600 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
          </motion.button>
        </motion.div>

        {/* 浮遊する音符アニメーション - クライアントサイドでのみ表示 */}
        {isClient && (
          <div className="fixed inset-0 pointer-events-none z-0">
            {[...Array(6)].map((_, i) => {
              // 固定されたシード値を使用してランダム性を制御
              const seed = i * 123.456;
              const xPos = (Math.sin(seed) * 0.5 + 0.5) * windowSize.width;
              const animationDelay = (i * 0.8) % 5;
              const duration = 12 + (i * 2);
              
              return (
                <motion.div
                  key={i}
                  className="absolute text-white/10 text-4xl z-0"
                  initial={{
                    x: xPos,
                    y: windowSize.height + 50,
                  }}
                  animate={{
                    y: -50,
                    x: xPos + (Math.cos(seed) * 100),
                  }}
                  transition={{
                    duration: duration,
                    repeat: Infinity,
                    delay: animationDelay,
                    ease: "linear"
                  }}
                >
                  ♪
                </motion.div>
              )
            })}
          </div>
        )}
      </div>
    </div>
  )
}
