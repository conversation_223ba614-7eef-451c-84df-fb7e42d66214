# -*- coding: utf-8 -*-
import os
import sys
import locale

# UTF-8エンコーディングを強制設定
if sys.platform.startswith('win'):
    # Windows環境の場合
    os.environ['PYTHONIOENCODING'] = 'utf-8'
else:
    # Unix/Linux/macOS環境の場合
    locale.setlocale(locale.LC_ALL, 'C.UTF-8')

from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import List, Dict, Optional
from openai import OpenAI
from datetime import datetime
from dotenv import load_dotenv
import requests
import json

# .envファイルを読み込み
load_dotenv()

app = FastAPI(title="AI歌詞クリエーター API", version="1.0.0")

# CORS設定
app.add_middleware(
    CORSMiddleware,
    allow_origins=[
        "http://localhost:3000",  # フロントエンドのURL（デフォルト）
        "http://localhost:3001",  # フロントエンドのURL（ポート変更時）
    ],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# OpenRouter クライアントの初期化（OpenAI SDKを使用）
# OpenRouterのAPIリファレンスに従って設定
openrouter_api_key = os.getenv("OPENROUTER_API_KEY")
print(f"OpenRouter API Key loaded: {'✓' if openrouter_api_key else '✗'}")

if openrouter_api_key:
    print(f"API Key starts with: {openrouter_api_key[:10]}...")

# 環境変数でエンコーディングを強制設定
os.environ['PYTHONIOENCODING'] = 'utf-8'
os.environ['LC_ALL'] = 'C.UTF-8'

client = OpenAI(
    base_url="https://openrouter.ai/api/v1",  # OpenRouterのエンドポイント
    api_key=openrouter_api_key,
    timeout=30.0,  # タイムアウト設定を追加
)

# 利用可能なモデル定義
AVAILABLE_MODELS = {
    "openai/gpt-4o": {
        "name": "GPT-4o",
        "provider": "OpenAI",
        "description": "OpenAIの最新マルチモーダルモデル。高品質で創造的な歌詞生成",
        "cost": "高コスト"
    },
    "openai/gpt-4o-mini": {
        "name": "GPT-4o Mini",
        "provider": "OpenAI", 
        "description": "GPT-4oの軽量版。バランスの良い品質とコスト",
        "cost": "中コスト"
    },
    "anthropic/claude-3.5-sonnet": {
        "name": "Claude 3.5 Sonnet",
        "provider": "Anthropic",
        "description": "詩的で感情豊かな表現が得意。日本語の歌詞作成に優秀",
        "cost": "高コスト"
    },
    "anthropic/claude-3-haiku": {
        "name": "Claude 3 Haiku", 
        "provider": "Anthropic",
        "description": "高速で効率的。シンプルで美しい歌詞を生成",
        "cost": "低コスト"
    },
    "google/gemini-flash-1.5": {
        "name": "Gemini Flash 1.5",
        "provider": "Google",
        "description": "高速で創造的。多彩な表現が可能",
        "cost": "低コスト"
    },
    "meta-llama/llama-3.1-70b-instruct": {
        "name": "Llama 3.1 70B",
        "provider": "Meta",
        "description": "オープンソースの高性能モデル。コストパフォーマンス重視",
        "cost": "低コスト"
    }
}

# データモデル
class SongConfig(BaseModel):
    pattern: str
    genre: str
    bpm: int
    duration: int
    theme: str
    syllables: Dict[str, int]
    model: Optional[str] = "openai/gpt-4o-mini"  # デフォルトモデル

class LyricsPart(BaseModel):
    type: str
    content: str
    syllableCount: int

class LyricsResponse(BaseModel):
    lyrics: List[LyricsPart]
    config: SongConfig
    generatedAt: str

# J-POP構成パターンの定義
SONG_PATTERNS = {
    "standard": {
        "name": "王道パターン",
        "structure": ["Aメロ1", "Bメロ1", "サビ1", "Aメロ2", "Bメロ2", "サビ2", "Cメロ", "大サビ"],
        "description": "起承転結の流れで聴きやすく、心理的な満足感を提供"
    },
    "chorus-first": {
        "name": "サビ始まり型",
        "structure": ["サビ1", "Aメロ1", "Bメロ1", "サビ2", "Aメロ2", "Bメロ2", "サビ3"],
        "description": "即座にリスナーの心を掴み、キャッチーさを重視"
    },
    "no-pre-chorus": {
        "name": "Bメロ省略型",
        "structure": ["Aメロ1", "サビ1", "Aメロ2", "サビ2", "Cメロ", "サビ3"],
        "description": "シンプルで疾走感があり、メッセージを直接的に伝える"
    },
    "with-bridge": {
        "name": "Cメロ/大サビ追加型",
        "structure": ["Aメロ1", "Bメロ1", "サビ1", "Aメロ2", "Bメロ2", "サビ2", "Cメロ", "Dメロ", "大サビ"],
        "description": "ストーリー性や緩急を強化し、楽曲に深みと多層性をもたらす"
    }
}

# ジャンル別の特徴
GENRE_CHARACTERISTICS = {
    "rock": {
        "mood": "力強く、エネルギッシュ",
        "themes": "挑戦、反抗、情熱、青春",
        "language_style": "直接的で力強い表現、短いフレーズ"
    },
    "soul": {
        "mood": "深く、感情的",
        "themes": "愛、別れ、人生の深み、内省",
        "language_style": "感情豊かで詩的な表現、長めのフレーズ"
    },
    "ballad": {
        "mood": "優しく、叙情的",
        "themes": "愛、思い出、希望、癒し",
        "language_style": "美しく繊細な表現、情景描写重視"
    },
    "acoustic": {
        "mood": "自然で、温かい",
        "themes": "日常、自然、素朴な愛、成長",
        "language_style": "親しみやすく自然な表現、比喩多用"
    },
    "pops": {
        "mood": "明るく、キャッチー",
        "themes": "恋愛、友情、夢、前向きさ",
        "language_style": "覚えやすく親しみやすい表現、リズム重視"
    }
}

def create_lyrics_prompt(config: SongConfig, part_type: str, previous_parts: List[LyricsPart]) -> str:
    """歌詞生成用のプロンプトを作成"""
    
    pattern_info = SONG_PATTERNS.get(config.pattern, SONG_PATTERNS["standard"])
    genre_info = GENRE_CHARACTERISTICS.get(config.genre, GENRE_CHARACTERISTICS["pops"])
    
    # 各パートの役割定義
    part_roles = {
        "Aメロ": "物語の導入部。情景描写、時間や場所の設定、登場人物の状況を具体的に描写し、リスナーを物語に引き込む。",
        "Bメロ": "サビへの橋渡し。Aメロで提示された状況に変化を加え、感情の高まりを構築。葛藤、疑問、決意などを描く。",
        "サビ": "楽曲の核心メッセージ。最も重要な感情や想いを凝縮し、記憶に残りやすいキャッチーな表現で歌い上げる。",
        "Cメロ": "視点の転換や深層心理の描写。物語に新たな側面を加え、最後のサビへの期待感を高める。",
        "Dメロ": "物語の深化。Cメロとは異なる角度から感情や状況を描写し、楽曲に更なる深みを与える。",
        "大サビ": "最終的な感動とメッセージの再強調。物語の結末や未来への希望、強い決意を歌い上げる。"
    }
    
    # 前の歌詞の内容を要約
    previous_context = ""
    if previous_parts:
        previous_context = "\n\n【これまでの歌詞の流れ】\n"
        for part in previous_parts:
            previous_context += f"{part.type}: {part.content[:50]}...\n"
    
    # 音節数の指定
    syllable_target = config.syllables.get(part_type.lower().replace("メロ", "").replace("サビ", "chorus").replace("verse", "verse").replace("pre", "preChorus").replace("bridge", "bridge"), 8)
    
    prompt = f"""
あなたは日本のJ-POP歌詞の専門家です。以下の条件に基づいて、{part_type}の歌詞を作成してください。

【楽曲設定】
- 構成パターン: {pattern_info['name']} - {pattern_info['description']}
- ジャンル: {config.genre.upper()} - {genre_info['mood']}
- テーマ: {config.theme}
- BPM: {config.bpm}
- 楽曲時間: {config.duration}秒

【{part_type}の役割】
{part_roles.get(part_type.replace("1", "").replace("2", "").replace("3", ""), "楽曲の一部として物語を展開する。")}

【ジャンル特徴】
- 雰囲気: {genre_info['mood']}
- 主要テーマ: {genre_info['themes']}
- 言語スタイル: {genre_info['language_style']}

【制約条件】
- 目標音節数: 約{syllable_target}音節（1行あたり8-12音節程度）
- 4行構成を基本とする
- 日本語として自然で歌いやすい
- テーマと一貫性を保つ
- 感情的に響く表現を使用

{previous_context}

上記を踏まえ、{part_type}の歌詞のみを出力してください。説明や解説は不要です。
"""
    
    return prompt

async def generate_lyrics_part(config: SongConfig, part_type: str, previous_parts: List[LyricsPart]) -> LyricsPart:
    """OpenRouter APIを使用して歌詞の一部を生成"""
    
    if not client.api_key:
        # OpenRouter APIキーが設定されていない場合のダミーデータ
        dummy_lyrics = {
            "Aメロ1": "夜空に輝く星たちが\n僕らの未来を照らしてる\n歩き続けた道のりで\n見つけた大切なもの",
            "Bメロ1": "時には迷うこともあるけど\n君がいれば大丈夫\n心の奥で響いてる\n愛の歌声",
            "サビ1": "愛を歌おう 心から\n響かせよう この想いを\n永遠に続く メロディーに\n込めて届けよう",
            "Aメロ2": "季節は巡り変わっても\n変わらない想いがここにある\n君と過ごした日々の中で\n育んだ絆の歌"
        }
        
        content = dummy_lyrics.get(part_type, f"【{part_type}】\nダミー歌詞です\nOPENROUTER_API_KEYを設定してください\n実際の歌詞生成のため\nAPIキーが必要です")
        return LyricsPart(
            type=part_type,
            content=content,
            syllableCount=len(content.replace("\n", ""))
        )
    
    try:
        prompt = create_lyrics_prompt(config, part_type, previous_parts)
        
        # 選択されたモデルを使用（デフォルトはgpt-4o-mini）
        selected_model = config.model or "openai/gpt-4o-mini"
        print(f"Using model: {selected_model} for {part_type}")
        
        # システムメッセージ
        system_message = "あなたは日本のJ-POP歌詞の専門家です。感情豊かで記憶に残る歌詞を作成します。"

        print(f"Generating lyrics for {part_type} using requests library...")

        # requestsライブラリを使用してOpenRouter APIに直接接続
        # エンコーディング問題を回避するため
        headers = {
            "Authorization": f"Bearer {openrouter_api_key}",
            "Content-Type": "application/json; charset=utf-8",
            "HTTP-Referer": "http://localhost:3000",
            "X-Title": "AI歌詞クリエーター",
        }

        payload = {
            "model": selected_model,
            "messages": [
                {"role": "system", "content": system_message},
                {"role": "user", "content": prompt}
            ],
            "max_tokens": 200,
            "temperature": 0.8
        }

        # UTF-8エンコーディングを明示的に指定してリクエストを送信
        response = requests.post(
            "https://openrouter.ai/api/v1/chat/completions",
            headers=headers,
            json=payload,  # jsonパラメータを使用してUTF-8エンコーディングを自動処理
            timeout=30
        )

        if response.status_code != 200:
            print(f"HTTP Error: {response.status_code}")
            print(f"Response: {response.text}")
            raise Exception(f"HTTP {response.status_code}: {response.text}")

        response_data = response.json()

        if 'choices' not in response_data or not response_data['choices']:
            raise Exception("No choices in response")

        generated_content = response_data['choices'][0]['message']['content']

        if not generated_content:
            raise Exception("Empty response from API")

        # 生成された歌詞の音節数を計算
        syllable_count = len(generated_content.replace("\n", "").replace(" ", ""))

        print(f"Generated lyrics for {part_type}: {generated_content[:50]}...")

        return LyricsPart(
            type=part_type,
            content=generated_content,
            syllableCount=syllable_count
        )
        
    except Exception as e:
        # エラー時のフォールバック - OpenRouterのエラー形式を考慮
        error_message = str(e)
        print(f"Error generating lyrics for {part_type}: {error_message}")
        
        # OpenRouterのエラーレスポンスの場合、詳細な情報を表示
        if hasattr(e, 'response') and hasattr(e.response, 'json'):
            try:
                error_data = e.response.json()
                if 'error' in error_data:
                    error_message = error_data['error'].get('message', error_message)
                    print(f"OpenRouter API Error: {error_message}")
            except:
                pass
        
        return LyricsPart(
            type=part_type,
            content=f"【{part_type}】\n歌詞生成中にエラーが発生しました\n{config.theme}をテーマに\n美しい歌詞を作成予定です\nモデル: {config.model or 'デフォルト'}\nエラー: {error_message[:100]}...",
            syllableCount=32
        )

@app.get("/")
async def root():
    return {"message": "AI歌詞クリエーター API", "version": "1.0.0"}

@app.post("/generate-lyrics", response_model=LyricsResponse)
async def generate_lyrics(config: SongConfig):
    """歌詞を生成するエンドポイント"""
    
    try:
        pattern_info = SONG_PATTERNS.get(config.pattern, SONG_PATTERNS["standard"])
        structure = pattern_info["structure"]
        
        lyrics_parts = []
        
        # 各パートを順番に生成
        for part_type in structure:
            lyrics_part = await generate_lyrics_part(config, part_type, lyrics_parts)
            lyrics_parts.append(lyrics_part)
        
        return LyricsResponse(
            lyrics=lyrics_parts,
            config=config,
            generatedAt=datetime.now().isoformat()
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"歌詞生成中にエラーが発生しました: {str(e)}")

@app.get("/patterns")
async def get_patterns():
    """利用可能な楽曲パターンを取得"""
    return SONG_PATTERNS

@app.get("/genres")
async def get_genres():
    """利用可能なジャンルを取得"""
    return GENRE_CHARACTERISTICS

@app.get("/models")
async def get_models():
    """利用可能なLLMモデルを取得"""
    return AVAILABLE_MODELS

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000) 